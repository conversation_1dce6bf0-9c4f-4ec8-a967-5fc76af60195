// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calendar_repo.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$calendarRepoHash() => r'9e816570979f5dad25280d8df952ca870f9cd13e';

/// See also [calendarRepo].
@ProviderFor(calendarRepo)
final calendarRepoProvider = AutoDisposeProvider<CalendarRepo>.internal(
  calendarRepo,
  name: r'calendarRepoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$calendarRepoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CalendarRepoRef = AutoDisposeProviderRef<CalendarRepo>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
