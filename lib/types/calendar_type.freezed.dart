// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'calendar_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CalendarState {
  DateTime get time => throw _privateConstructorUsedError;
  CalendarType get type => throw _privateConstructorUsedError;
  FlowerTimeline? get timeline => throw _privateConstructorUsedError;
  List<String>? get photos => throw _privateConstructorUsedError;
  MaintenanceRecord? get nurture => throw _privateConstructorUsedError;
  Flower get flower => throw _privateConstructorUsedError;

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CalendarStateCopyWith<CalendarState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CalendarStateCopyWith<$Res> {
  factory $CalendarStateCopyWith(
          CalendarState value, $Res Function(CalendarState) then) =
      _$CalendarStateCopyWithImpl<$Res, CalendarState>;
  @useResult
  $Res call(
      {DateTime time,
      CalendarType type,
      FlowerTimeline? timeline,
      List<String>? photos,
      MaintenanceRecord? nurture,
      Flower flower});
}

/// @nodoc
class _$CalendarStateCopyWithImpl<$Res, $Val extends CalendarState>
    implements $CalendarStateCopyWith<$Res> {
  _$CalendarStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? time = null,
    Object? type = null,
    Object? timeline = freezed,
    Object? photos = freezed,
    Object? nurture = freezed,
    Object? flower = null,
  }) {
    return _then(_value.copyWith(
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as CalendarType,
      timeline: freezed == timeline
          ? _value.timeline
          : timeline // ignore: cast_nullable_to_non_nullable
              as FlowerTimeline?,
      photos: freezed == photos
          ? _value.photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      nurture: freezed == nurture
          ? _value.nurture
          : nurture // ignore: cast_nullable_to_non_nullable
              as MaintenanceRecord?,
      flower: null == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as Flower,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CalendarStateImplCopyWith<$Res>
    implements $CalendarStateCopyWith<$Res> {
  factory _$$CalendarStateImplCopyWith(
          _$CalendarStateImpl value, $Res Function(_$CalendarStateImpl) then) =
      __$$CalendarStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime time,
      CalendarType type,
      FlowerTimeline? timeline,
      List<String>? photos,
      MaintenanceRecord? nurture,
      Flower flower});
}

/// @nodoc
class __$$CalendarStateImplCopyWithImpl<$Res>
    extends _$CalendarStateCopyWithImpl<$Res, _$CalendarStateImpl>
    implements _$$CalendarStateImplCopyWith<$Res> {
  __$$CalendarStateImplCopyWithImpl(
      _$CalendarStateImpl _value, $Res Function(_$CalendarStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? time = null,
    Object? type = null,
    Object? timeline = freezed,
    Object? photos = freezed,
    Object? nurture = freezed,
    Object? flower = null,
  }) {
    return _then(_$CalendarStateImpl(
      time: null == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as DateTime,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as CalendarType,
      timeline: freezed == timeline
          ? _value.timeline
          : timeline // ignore: cast_nullable_to_non_nullable
              as FlowerTimeline?,
      photos: freezed == photos
          ? _value._photos
          : photos // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      nurture: freezed == nurture
          ? _value.nurture
          : nurture // ignore: cast_nullable_to_non_nullable
              as MaintenanceRecord?,
      flower: null == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as Flower,
    ));
  }
}

/// @nodoc

class _$CalendarStateImpl implements _CalendarState {
  const _$CalendarStateImpl(
      {required this.time,
      required this.type,
      required this.timeline,
      required final List<String>? photos,
      required this.nurture,
      required this.flower})
      : _photos = photos;

  @override
  final DateTime time;
  @override
  final CalendarType type;
  @override
  final FlowerTimeline? timeline;
  final List<String>? _photos;
  @override
  List<String>? get photos {
    final value = _photos;
    if (value == null) return null;
    if (_photos is EqualUnmodifiableListView) return _photos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final MaintenanceRecord? nurture;
  @override
  final Flower flower;

  @override
  String toString() {
    return 'CalendarState(time: $time, type: $type, timeline: $timeline, photos: $photos, nurture: $nurture, flower: $flower)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalendarStateImpl &&
            (identical(other.time, time) || other.time == time) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.timeline, timeline) ||
                other.timeline == timeline) &&
            const DeepCollectionEquality().equals(other._photos, _photos) &&
            (identical(other.nurture, nurture) || other.nurture == nurture) &&
            (identical(other.flower, flower) || other.flower == flower));
  }

  @override
  int get hashCode => Object.hash(runtimeType, time, type, timeline,
      const DeepCollectionEquality().hash(_photos), nurture, flower);

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalendarStateImplCopyWith<_$CalendarStateImpl> get copyWith =>
      __$$CalendarStateImplCopyWithImpl<_$CalendarStateImpl>(this, _$identity);
}

abstract class _CalendarState implements CalendarState {
  const factory _CalendarState(
      {required final DateTime time,
      required final CalendarType type,
      required final FlowerTimeline? timeline,
      required final List<String>? photos,
      required final MaintenanceRecord? nurture,
      required final Flower flower}) = _$CalendarStateImpl;

  @override
  DateTime get time;
  @override
  CalendarType get type;
  @override
  FlowerTimeline? get timeline;
  @override
  List<String>? get photos;
  @override
  MaintenanceRecord? get nurture;
  @override
  Flower get flower;

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalendarStateImplCopyWith<_$CalendarStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
