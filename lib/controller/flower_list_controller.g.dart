// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flower_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$flowerListControllerHash() =>
    r'1e86875540463c986aa7645cd5ef074f343a96cd';

/// See also [flowerListController].
@ProviderFor(flowerListController)
final flowerListControllerProvider =
    AutoDisposeProvider<FlowerListController>.internal(
  flowerListController,
  name: r'flowerListControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$flowerListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FlowerListControllerRef = AutoDisposeProviderRef<FlowerListController>;
String _$flowerListLoaderHash() => r'193226dd0c4c6b662198b3bc46441490f2b4bc3c';

/// See also [flowerListLoader].
@ProviderFor(flowerListLoader)
final flowerListLoaderProvider =
    AutoDisposeFutureProvider<List<Flower>>.internal(
  flowerListLoader,
  name: r'flowerListLoaderProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$flowerListLoaderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FlowerListLoaderRef = AutoDisposeFutureProviderRef<List<Flower>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
