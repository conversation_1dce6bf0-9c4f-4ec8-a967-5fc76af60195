import 'package:flower_timemachine/models/flower_timeline_item.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'show_photo_controller.g.dart';


@riverpod
ShowPhotoController showPhotoController(
    ShowPhotoControllerRef ref,
    int flowerId,
    List<FlowerTimelineItem> flowerTimelines,
    PhotoRecord currentPhotoRecord
) => ShowPhotoController.create(flowerId, flowerTimelines, currentPhotoRecord);


class ShowPhotoController extends StateNotifier<List<PhotoRecord>> {
  static const _limit = 1;

  final int flowerId;

  final int initialPage;

  bool _hasMore = true;

  ShowPhotoController(this.flowerId, this.initialPage, super.state) {
    if (initialPage == (state.length - 1)) {
      // 预加载
      loadMore();
    }
  }

  Future<void> loadMore() async {
    final offset = state.length;
    final newData = await PhotoRecord.getByFlowerId(flowerId, offset: offset, limit: _limit, timeOrder: 'DESC');

    _hasMore = newData.length == _limit;

    if (newData.isEmpty) {
      return;
    }


    state = [...state, ...newData];
  }

  static ShowPhotoController create(int flowerId, List<FlowerTimelineItem> flowerTimelines, PhotoRecord currentRecord) {
    final List<PhotoRecord> photos = [];
    int initialPage = 0;

    int index = 0;
    for (final item in flowerTimelines) {
      for (final photo in item.photos) {
        photos.add(photo);

        if (currentRecord.id == photo.id) {
          initialPage = index;
        }

        index += 1;
      }
    }

    return ShowPhotoController(flowerId, initialPage, photos);
  }

  bool get hasMore => _hasMore;

  List<PhotoRecord> get items => List.unmodifiable(state);
}