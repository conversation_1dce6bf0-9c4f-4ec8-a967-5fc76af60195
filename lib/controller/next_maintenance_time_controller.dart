import 'package:flower_timemachine/models/next_task_time.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'nitification_manager.dart';


class NextMaintenanceTimeController {
  final List<NextMaintenanceTime> _state;
  final FutureProviderRef ref;

  NextMaintenanceTimeController(List<NextMaintenanceTime> state, this.ref) : _state = state;

  Future<int> deleteNurtureType(int nurtureTypeId) async {
    // 因为删除了最近的养护记录，导致下一次最近的养护时间可能发生变化，变化的话需要也同步修改通知时间
    // 具体逻辑：收集与将被删除养护类型的同一 flower id 的时间戳，并且排序和记录相同时间戳的出现次数
    //        如果将被删除的时间戳小于等于最小时间时间戳，并且最小时间戳出现的次数为1，就表示需要更改通知时间
    //        用次小时间戳作为新的通知时间
    final Map<int, Map<String, dynamic>> flowerNurtureInfo = {};
    for (final item in state) {
      final info = flowerNurtureInfo.putIfAbsent(item.flowerId, () => {});

      // 需要删除的养护时间
      if (item.type.id == nurtureTypeId) {
        info["deleteTime"] = item.nextTime;
      }

      Map<int, int> timesInfo = info.putIfAbsent("timesInfo", () => <int, int>{});
      // 统计时间出现的次数
      timesInfo[item.nextTime] = (timesInfo[item.nextTime] ?? 0) + 1;
    }

    for (final entry in flowerNurtureInfo.entries) {
      final deleteTime = entry.value["deleteTime"];
      if (deleteTime == null) {
        // 该花没有需要删除的 NextMaintenanceTime
        continue;
      }

      // Map<时间戳，这个时间戳的数量>
      Map<int, int> timesInfo = entry.value["timesInfo"];
      final List<int> times = timesInfo.keys.toList();

      // 排序时间戳，便于判断需要删除的时间是不是通知时间，以及查找新的通知时间
      times.sort();

      // 如果需要删除的养护时间是将要通知的时间，并且这个时间只有这一条养护记录
      if (deleteTime <= times[0] && timesInfo[times[0]] == 1) {
        // 新的最近养护时间（新的通知时间）
        DateTime? newAlarm;

        // 如果 times.length == 1 就意味着这朵花只有这条养护记录，删除了也就没有养护了，就不需要通知时间了
        if (times.length > 1) {
          newAlarm = DateTime.fromMillisecondsSinceEpoch(times[1] * 1000);
        }

        // 更新通知
        (await NotificationManger.instance).update(
            entry.key,
            DateTime.fromMillisecondsSinceEpoch(deleteTime * 1000),
            newAlarm
        );
      }
    }

    return await NextMaintenanceTime.deleteByNurtureType(nurtureTypeId);
  }

  List<NextMaintenanceTime> get state => List.unmodifiable(_state);
}

final nextMaintenanceTimeControllerProvider = FutureProvider.autoDispose<NextMaintenanceTimeController>((ref) async {
  final data = await NextMaintenanceTime.getAll();
  return NextMaintenanceTimeController(data, ref);
});