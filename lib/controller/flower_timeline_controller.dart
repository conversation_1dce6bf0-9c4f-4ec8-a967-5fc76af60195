import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/flower_timeline_editor_item.dart';
import 'package:flower_timemachine/models/media_item.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/flower_timeline_item.dart';

class FlowerTimelineController extends StateNotifier<AsyncValue<List<FlowerTimelineItem>>> {
  static const _limit = 20;

  final Flower _flower;

  bool _hasMore;

  bool _isLoading = false;

  FlowerTimelineController(Flower flower) : _flower = flower, _hasMore = true, super(const AsyncValue.loading()) {
    _init();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    _isLoading = true;

    final offset = state.requireValue.length;
    final newData = await FlowerTimelineItem.get(_flower.id, offset: offset, limit: _limit);

    _hasMore = newData.length == _limit;

    state = AsyncValue.data([...state.requireValue, ...newData]);

    _isLoading = false;
  }

  static Future<void> addNewRecordWithMediaItems(int flowerId, FlowerTimelineEditorItem item, List<MediaItem> mediaItems) async {
    await FlowerTimelineItem.createWithMediaItems(flowerId, item.text, mediaItems, item.dateTime);
  }

  static Future<void> updateRecordWithMediaItems(
      FlowerTimelineItem oldItem, FlowerTimelineEditorItem newItem, int flowerId, List<MediaItem> mediaItems
  ) async {
    await oldItem.updateWithMediaItems(newItem.text, mediaItems, newItem.dateTime, flowerId);
  }

  Future<void> deleteRecord(FlowerTimelineItem item) async {
    final list = state.requireValue;
    int index = list.indexOf(item);
    if (index < 0) {
      return;
    }

    await item.delete();
    state = AsyncValue.data([
      for (final curItem in list)
        if (curItem != item) curItem
    ]);
  }

  void _init() async {
    final result = await FlowerTimelineItem.get(_flower.id, offset: 0, limit: _limit);
    state = AsyncValue.data(result);
  }

  List<FlowerTimelineItem> get items => List.unmodifiable(state.requireValue);

  bool get hasMore => _hasMore;
}

final flowerTimelineControllerProvider = StateNotifierProvider.autoDispose.family<
    FlowerTimelineController,
    AsyncValue<List<FlowerTimelineItem>>,
    Flower
>(
    (ref, flower) => FlowerTimelineController(flower)
);