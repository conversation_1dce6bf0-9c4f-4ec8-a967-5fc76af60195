// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flower_pending_auto_nurture_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$flowerPendingAutoNurtureControllerHash() =>
    r'b12917a785ae35653bd065348bb5f0ca26fa6395';

/// See also [FlowerPendingAutoNurtureController].
@ProviderFor(FlowerPendingAutoNurtureController)
final flowerPendingAutoNurtureControllerProvider = AutoDisposeNotifierProvider<
    FlowerPendingAutoNurtureController, List<Flower>>.internal(
  FlowerPendingAutoNurtureController.new,
  name: r'flowerPendingAutoNurtureControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$flowerPendingAutoNurtureControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FlowerPendingAutoNurtureController
    = AutoDisposeNotifier<List<Flower>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
