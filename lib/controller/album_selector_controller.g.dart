// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'album_selector_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$queryAlbumFilterDataHash() =>
    r'7624fd58ee76833ebf5fc2b53f0e9e462c2faa1a';

/// See also [queryAlbumFilterData].
@ProviderFor(queryAlbumFilterData)
final queryAlbumFilterDataProvider =
    AutoDisposeFutureProvider<List<AlbumFilterData>>.internal(
  queryAlbumFilterData,
  name: r'queryAlbumFilterDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$queryAlbumFilterDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef QueryAlbumFilterDataRef
    = AutoDisposeFutureProviderRef<List<AlbumFilterData>>;
String _$albumCurrentFilterHash() =>
    r'4a607e0a57350ab2e85337deb43ef4f40f50727a';

/// See also [AlbumCurrentFilter].
@ProviderFor(AlbumCurrentFilter)
final albumCurrentFilterProvider =
    NotifierProvider<AlbumCurrentFilter, List<AlbumFilterData>>.internal(
  AlbumCurrentFilter.new,
  name: r'albumCurrentFilterProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$albumCurrentFilterHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AlbumCurrentFilter = Notifier<List<AlbumFilterData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
