
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/show_photo_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:photo_view/photo_view.dart';
import 'package:pull_down_button/pull_down_button.dart';

import '../../models/flower_timeline_item.dart';
import '../../models/photo_record.dart';
import '../../widgets/media_renderer.dart';

class ShowPhotoPage extends ConsumerStatefulWidget {
  final PhotoRecord currentPhotoRecord;
  final List<FlowerTimelineItem> flowerTimelines;
  final int flowerId;

  const ShowPhotoPage({
    super.key,
    required this.currentPhotoRecord,
    required this.flowerTimelines,
    required this.flowerId,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ShowPhotoPageState();
}

class _ShowPhotoPageState extends ConsumerState<ShowPhotoPage> {
  PageController? pageController;
  int _currentPage = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.black,
          elevation: 0,
          actions: [
            _ShowPhotoButton(itemBuilder: moreItemButton, child: const Icon(Icons.more_vert, size: 30)),
            const SizedBox(width: 10),
          ],
        ),
        body: Consumer(builder: (context, ref, _) {
          final controller = ref.watch(
              showPhotoControllerProvider(widget.flowerId, widget.flowerTimelines, widget.currentPhotoRecord)
          );
          pageController?.dispose();
          pageController = PageController(initialPage: controller.initialPage);
          return PhotoViewGallery.builder(
            pageController: pageController,
            scrollPhysics: const BouncingScrollPhysics(),
            gaplessPlayback: true,
            allowImplicitScrolling: true,
            builder: photoViewBuilder,
            itemCount: controller.items.length,
            onPageChanged: onPageChange,
          );
        })
    );
  }

  PhotoViewGalleryPageOptions photoViewBuilder(BuildContext context, int index) {
    final controller = ref.read(
        showPhotoControllerProvider(widget.flowerId, widget.flowerTimelines, widget.currentPhotoRecord)
    );

    final item = controller.items[index];
    return PhotoViewGalleryPageOptions.customChild(
      child: MediaRenderer(
        path: item.file,
        mediaType: item.mediaType,
        isVisible: index == _currentPage, // 只有当前页面的视频才会播放
      ),
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 2,

    );
  }

  Future<void> onPageChange(int index) async {
    setState(() {
      _currentPage = index; // 更新当前页面索引
    });

    final controller = ref.read(
        showPhotoControllerProvider(widget.flowerId, widget.flowerTimelines, widget.currentPhotoRecord)
    );

    if (!controller.hasMore || index < (controller.items.length - 1)) {
      return;
    }

    controller.loadMore();
  }

  List<PullDownMenuEntry> moreItemButton(BuildContext context) {
    return [
      PullDownMenuItem(
          title: 'show_photo_page.save_album'.tr(),
          onTap: onSaveGallery
      )
    ];
  }

  void onSaveGallery() async {
    final page = pageController?.page?.truncate();
    if (page == null) {
      return;
    }

    final controller = ref.read(
        showPhotoControllerProvider(widget.flowerId, widget.flowerTimelines, widget.currentPhotoRecord)
    );
    final record = controller.items[page];

    await ImageGallerySaver.saveFile(record.file);

    Fluttertoast.showToast(msg: "show_photo_page.save_success".tr(), toastLength: Toast.LENGTH_LONG);
  }

  @override
  void initState() {
    final controller = ref.read(
        showPhotoControllerProvider(widget.flowerId, widget.flowerTimelines, widget.currentPhotoRecord)
    );
    _currentPage = controller.initialPage;
    super.initState();
  }
}

class _ShowPhotoButton extends StatelessWidget {
  final PullDownMenuItemBuilder itemBuilder;
  final Widget child;

  const _ShowPhotoButton({required this.itemBuilder, required this.child});

  @override
  Widget build(BuildContext context) {
    return PullDownButton(
        itemBuilder: itemBuilder,
        buttonBuilder: (context, showMenu) => GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: showMenu,
          child: child,
        ));
  }
}