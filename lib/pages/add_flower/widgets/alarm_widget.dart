import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flower_timemachine/pages/add_flower/widgets/cycle_picker_bottom_sheet.dart';
import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddFlowerAlarmWidget extends ConsumerWidget {
  const AddFlowerAlarmWidget({
    super.key,
    required this.type,
    required this.pickerData,
    required this.flower,
  });

  final NurtureType type;
  final List<String> pickerData;
  final Flower? flower;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final month = DateTime.now().month;
    final monthCycles = ref.watch(addFlowerMonthCycleProvider(flower));
    final cycle = monthCycles.when(
        data: (cycleState) => cycleState.getEffectiveCycleForTypeAndMonth(type, month),
        error: (e, s) => monthlyCycleDataUnset,
        loading: () => monthlyCycleDataUnset
    );

    Widget cycleText;
    if (cycle == monthlyCycleDataUnset) {
      cycleText = Text('monthly_cycle_settings.unset'.tr(), style: const TextStyle(color: Colors.black54));
    } else if (cycle == monthlyCycleDataInherit) {
      cycleText = Text('monthly_cycle_settings.inherit'.tr(), style: const TextStyle(color: Colors.black54));
    } else {
      cycleText = Text('$cycle${'monthly_cycle_settings.days_unit'.tr()}');
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      child: buildBody(cycleText),
      onTap: () => _showCyclePicker(context, ref),
    );
  }

  Widget buildBody(Widget cycle) {
    return Container(
        padding: const EdgeInsets.all(5),
        child: Row(children: [
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SvgPicture.asset(type.icon, width: 16, height: 16),
          ),
          Text(type.name),
          const Spacer(),
          cycle,
          const SizedBox(width: 5),
          const Icon(Icons.edit)
        ])
    );
  }

  void _showCyclePicker(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CyclePickerBottomSheet(
      ),
    );
  }
}
