import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flower_timemachine/widgets/date_picker_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final _dateFormatter = DateFormat.yMMMd();

class ArrivalTimeWidget extends ConsumerWidget {
  const ArrivalTimeWidget({super.key, required this.flower});

  final Flower? flower;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final arrivalTime = ref.watch(
      addFlowerControllerProvider(flower).select((asyncState) =>
        asyncState.when(
          data: (state) => state.arrivalTime,
          loading: () => DateTime.now(),
          error: (_, __) => DateTime.now(),
        )
      )
    );

    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("add_flower.arrival_time_title".tr(), textAlign: TextAlign.start),
          const SizedBox(height: 5),
          Container(
              padding: const EdgeInsets.all(10),
              constraints: const BoxConstraints(minHeight: 50, minWidth: double.infinity),
              decoration: BoxDecoration(
                  color: const Color(0xfff7f7f7),
                  border: Border.all(width: 1, color: Colors.transparent),
                  borderRadius: BorderRadius.circular(5)),
              child: GestureDetector(
                  onTap: () => onTapDate(context, ref, arrivalTime),
                  child: Row(
                    children: [
                      Expanded(
                          child: Text(
                            _dateFormatter.format(arrivalTime),
                            style: const TextStyle(fontSize: 18),
                          )),
                      const SizedBox(width: 5),
                      const Icon(Icons.arrow_forward_ios, color: Colors.grey)
                    ],
                  )))
        ],
      ),
    );
  }

  void onTapDate(BuildContext context, WidgetRef ref, DateTime currentArrivalTime) async {
    if (context.mounted) {
      final newDate = await FTMDatePickerDialog.show(context, "confirm".tr(),
          defaultDate: currentArrivalTime, mode: CupertinoDatePickerMode.date, maximumDate: DateTime.now());

      if (newDate == null) {
        return;
      }

      final setDate = currentArrivalTime.copyWith(year: newDate.year, month: newDate.month, day: newDate.day);
      final finalArrivalTime = DateTime.now().isBefore(setDate)
        ? DateTime.now()
        : currentArrivalTime.copyWith(year: newDate.year, month: newDate.month, day: newDate.day);

      ref.read(addFlowerControllerProvider(flower).notifier).setArrivalTime(finalArrivalTime);
    }
  }
}
