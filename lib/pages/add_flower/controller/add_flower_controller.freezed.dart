// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_flower_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AddFlowerState {
  Flower? get flower => throw _privateConstructorUsedError;
  FlowerMonthlyCycles get monthlyCycles => throw _privateConstructorUsedError;
  Uint8List? get newAvatarData => throw _privateConstructorUsedError;
  String? get newName => throw _privateConstructorUsedError;
  bool get isChange => throw _privateConstructorUsedError;
  List<TagInfo>? get newSelectedTags => throw _privateConstructorUsedError;
  DateTime? get newArrivalTime => throw _privateConstructorUsedError;

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddFlowerStateCopyWith<AddFlowerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddFlowerStateCopyWith<$Res> {
  factory $AddFlowerStateCopyWith(
          AddFlowerState value, $Res Function(AddFlowerState) then) =
      _$AddFlowerStateCopyWithImpl<$Res, AddFlowerState>;
  @useResult
  $Res call(
      {Flower? flower,
      FlowerMonthlyCycles monthlyCycles,
      Uint8List? newAvatarData,
      String? newName,
      bool isChange,
      List<TagInfo>? newSelectedTags,
      DateTime? newArrivalTime});
}

/// @nodoc
class _$AddFlowerStateCopyWithImpl<$Res, $Val extends AddFlowerState>
    implements $AddFlowerStateCopyWith<$Res> {
  _$AddFlowerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flower = freezed,
    Object? monthlyCycles = null,
    Object? newAvatarData = freezed,
    Object? newName = freezed,
    Object? isChange = null,
    Object? newSelectedTags = freezed,
    Object? newArrivalTime = freezed,
  }) {
    return _then(_value.copyWith(
      flower: freezed == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as Flower?,
      monthlyCycles: null == monthlyCycles
          ? _value.monthlyCycles
          : monthlyCycles // ignore: cast_nullable_to_non_nullable
              as FlowerMonthlyCycles,
      newAvatarData: freezed == newAvatarData
          ? _value.newAvatarData
          : newAvatarData // ignore: cast_nullable_to_non_nullable
              as Uint8List?,
      newName: freezed == newName
          ? _value.newName
          : newName // ignore: cast_nullable_to_non_nullable
              as String?,
      isChange: null == isChange
          ? _value.isChange
          : isChange // ignore: cast_nullable_to_non_nullable
              as bool,
      newSelectedTags: freezed == newSelectedTags
          ? _value.newSelectedTags
          : newSelectedTags // ignore: cast_nullable_to_non_nullable
              as List<TagInfo>?,
      newArrivalTime: freezed == newArrivalTime
          ? _value.newArrivalTime
          : newArrivalTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddFlowerStateImplCopyWith<$Res>
    implements $AddFlowerStateCopyWith<$Res> {
  factory _$$AddFlowerStateImplCopyWith(_$AddFlowerStateImpl value,
          $Res Function(_$AddFlowerStateImpl) then) =
      __$$AddFlowerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Flower? flower,
      FlowerMonthlyCycles monthlyCycles,
      Uint8List? newAvatarData,
      String? newName,
      bool isChange,
      List<TagInfo>? newSelectedTags,
      DateTime? newArrivalTime});
}

/// @nodoc
class __$$AddFlowerStateImplCopyWithImpl<$Res>
    extends _$AddFlowerStateCopyWithImpl<$Res, _$AddFlowerStateImpl>
    implements _$$AddFlowerStateImplCopyWith<$Res> {
  __$$AddFlowerStateImplCopyWithImpl(
      _$AddFlowerStateImpl _value, $Res Function(_$AddFlowerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flower = freezed,
    Object? monthlyCycles = null,
    Object? newAvatarData = freezed,
    Object? newName = freezed,
    Object? isChange = null,
    Object? newSelectedTags = freezed,
    Object? newArrivalTime = freezed,
  }) {
    return _then(_$AddFlowerStateImpl(
      flower: freezed == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as Flower?,
      monthlyCycles: null == monthlyCycles
          ? _value.monthlyCycles
          : monthlyCycles // ignore: cast_nullable_to_non_nullable
              as FlowerMonthlyCycles,
      newAvatarData: freezed == newAvatarData
          ? _value.newAvatarData
          : newAvatarData // ignore: cast_nullable_to_non_nullable
              as Uint8List?,
      newName: freezed == newName
          ? _value.newName
          : newName // ignore: cast_nullable_to_non_nullable
              as String?,
      isChange: null == isChange
          ? _value.isChange
          : isChange // ignore: cast_nullable_to_non_nullable
              as bool,
      newSelectedTags: freezed == newSelectedTags
          ? _value._newSelectedTags
          : newSelectedTags // ignore: cast_nullable_to_non_nullable
              as List<TagInfo>?,
      newArrivalTime: freezed == newArrivalTime
          ? _value.newArrivalTime
          : newArrivalTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$AddFlowerStateImpl extends _AddFlowerState {
  const _$AddFlowerStateImpl(
      {required this.flower,
      required this.monthlyCycles,
      this.newAvatarData,
      this.newName,
      this.isChange = false,
      final List<TagInfo>? newSelectedTags,
      this.newArrivalTime})
      : _newSelectedTags = newSelectedTags,
        super._();

  @override
  final Flower? flower;
  @override
  final FlowerMonthlyCycles monthlyCycles;
  @override
  final Uint8List? newAvatarData;
  @override
  final String? newName;
  @override
  @JsonKey()
  final bool isChange;
  final List<TagInfo>? _newSelectedTags;
  @override
  List<TagInfo>? get newSelectedTags {
    final value = _newSelectedTags;
    if (value == null) return null;
    if (_newSelectedTags is EqualUnmodifiableListView) return _newSelectedTags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? newArrivalTime;

  @override
  String toString() {
    return 'AddFlowerState(flower: $flower, monthlyCycles: $monthlyCycles, newAvatarData: $newAvatarData, newName: $newName, isChange: $isChange, newSelectedTags: $newSelectedTags, newArrivalTime: $newArrivalTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddFlowerStateImpl &&
            (identical(other.flower, flower) || other.flower == flower) &&
            (identical(other.monthlyCycles, monthlyCycles) ||
                other.monthlyCycles == monthlyCycles) &&
            const DeepCollectionEquality()
                .equals(other.newAvatarData, newAvatarData) &&
            (identical(other.newName, newName) || other.newName == newName) &&
            (identical(other.isChange, isChange) ||
                other.isChange == isChange) &&
            const DeepCollectionEquality()
                .equals(other._newSelectedTags, _newSelectedTags) &&
            (identical(other.newArrivalTime, newArrivalTime) ||
                other.newArrivalTime == newArrivalTime));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      flower,
      monthlyCycles,
      const DeepCollectionEquality().hash(newAvatarData),
      newName,
      isChange,
      const DeepCollectionEquality().hash(_newSelectedTags),
      newArrivalTime);

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddFlowerStateImplCopyWith<_$AddFlowerStateImpl> get copyWith =>
      __$$AddFlowerStateImplCopyWithImpl<_$AddFlowerStateImpl>(
          this, _$identity);
}

abstract class _AddFlowerState extends AddFlowerState {
  const factory _AddFlowerState(
      {required final Flower? flower,
      required final FlowerMonthlyCycles monthlyCycles,
      final Uint8List? newAvatarData,
      final String? newName,
      final bool isChange,
      final List<TagInfo>? newSelectedTags,
      final DateTime? newArrivalTime}) = _$AddFlowerStateImpl;
  const _AddFlowerState._() : super._();

  @override
  Flower? get flower;
  @override
  FlowerMonthlyCycles get monthlyCycles;
  @override
  Uint8List? get newAvatarData;
  @override
  String? get newName;
  @override
  bool get isChange;
  @override
  List<TagInfo>? get newSelectedTags;
  @override
  DateTime? get newArrivalTime;

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddFlowerStateImplCopyWith<_$AddFlowerStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
