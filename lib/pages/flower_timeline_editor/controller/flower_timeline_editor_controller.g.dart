// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flower_timeline_editor_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$flowerTimelineEditorControllerHash() =>
    r'fc1a85807e810fb817823d528dbc36e8a4cbb334';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$FlowerTimelineEditorController
    extends BuildlessAutoDisposeNotifier<FlowerTimelineEditorItem> {
  late final FlowerTimelineEditorItem? item;

  FlowerTimelineEditorItem build(
    FlowerTimelineEditorItem? item,
  );
}

/// See also [FlowerTimelineEditorController].
@ProviderFor(FlowerTimelineEditorController)
const flowerTimelineEditorControllerProvider =
    FlowerTimelineEditorControllerFamily();

/// See also [FlowerTimelineEditorController].
class FlowerTimelineEditorControllerFamily
    extends Family<FlowerTimelineEditorItem> {
  /// See also [FlowerTimelineEditorController].
  const FlowerTimelineEditorControllerFamily();

  /// See also [FlowerTimelineEditorController].
  FlowerTimelineEditorControllerProvider call(
    FlowerTimelineEditorItem? item,
  ) {
    return FlowerTimelineEditorControllerProvider(
      item,
    );
  }

  @override
  FlowerTimelineEditorControllerProvider getProviderOverride(
    covariant FlowerTimelineEditorControllerProvider provider,
  ) {
    return call(
      provider.item,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'flowerTimelineEditorControllerProvider';
}

/// See also [FlowerTimelineEditorController].
class FlowerTimelineEditorControllerProvider
    extends AutoDisposeNotifierProviderImpl<FlowerTimelineEditorController,
        FlowerTimelineEditorItem> {
  /// See also [FlowerTimelineEditorController].
  FlowerTimelineEditorControllerProvider(
    FlowerTimelineEditorItem? item,
  ) : this._internal(
          () => FlowerTimelineEditorController()..item = item,
          from: flowerTimelineEditorControllerProvider,
          name: r'flowerTimelineEditorControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$flowerTimelineEditorControllerHash,
          dependencies: FlowerTimelineEditorControllerFamily._dependencies,
          allTransitiveDependencies:
              FlowerTimelineEditorControllerFamily._allTransitiveDependencies,
          item: item,
        );

  FlowerTimelineEditorControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.item,
  }) : super.internal();

  final FlowerTimelineEditorItem? item;

  @override
  FlowerTimelineEditorItem runNotifierBuild(
    covariant FlowerTimelineEditorController notifier,
  ) {
    return notifier.build(
      item,
    );
  }

  @override
  Override overrideWith(FlowerTimelineEditorController Function() create) {
    return ProviderOverride(
      origin: this,
      override: FlowerTimelineEditorControllerProvider._internal(
        () => create()..item = item,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        item: item,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<FlowerTimelineEditorController,
      FlowerTimelineEditorItem> createElement() {
    return _FlowerTimelineEditorControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FlowerTimelineEditorControllerProvider &&
        other.item == item;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, item.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FlowerTimelineEditorControllerRef
    on AutoDisposeNotifierProviderRef<FlowerTimelineEditorItem> {
  /// The parameter `item` of this provider.
  FlowerTimelineEditorItem? get item;
}

class _FlowerTimelineEditorControllerProviderElement
    extends AutoDisposeNotifierProviderElement<FlowerTimelineEditorController,
        FlowerTimelineEditorItem> with FlowerTimelineEditorControllerRef {
  _FlowerTimelineEditorControllerProviderElement(super.provider);

  @override
  FlowerTimelineEditorItem? get item =>
      (origin as FlowerTimelineEditorControllerProvider).item;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
