// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/widgets.dart';

void main() {
  // Changes made in https://github.com/flutter/flutter/pull/44189
  const BuildContext buildContext = Element(myWidget);
  buildContext.dependOnInheritedElement(ancestor);
  buildContext.dependOnInheritedWidgetOfExactType<targetType>();
  buildContext.getElementForInheritedWidgetOfExactType<targetType>();
  buildContext.findAncestorWidgetOfExactType<targetType>();
  buildContext.findAncestorStateOfType<targetType>();
  buildContext.findRootAncestorStateOfType<targetType>();
  buildContext.findAncestorRenderObjectOfType<targetType>();
}
